import {Modu<PERSON>} from '@nestjs/common';
import {GraphQLModule} from '@nestjs/graphql';
import {ApolloDriver, ApolloDriverConfig} from '@nestjs/apollo';
import {MongooseModule} from '@nestjs/mongoose';
import {join} from 'path';
import {BoardModule} from './board/board.module';
import {SwimlaneModule} from './swimlane/swimlane.module';
import {CardModule} from './card/card.module';
import {CommentModule} from './comment/comment.module';
import {ChecklistModule} from './checklist/checklist.module';

@Module({
  imports: [
    MongooseModule.forRoot(process.env.MONGODB_URI || 'mongodb://localhost:27017/cryptodo'),
    GraphQLModule.forRoot<ApolloDriverConfig>({
      driver: ApolloDriver,
      autoSchemaFile: join(process.cwd(), 'src/schema.gql'),
      sortSchema: true,
      playground: true,
      context: ({ req, res }) => ({ req, res }),
      formatError: (error) => {
        console.error('GraphQL Error:', error);
        return error;
      },
    }),
    BoardModule,
    SwimlaneModule,
    CardModule,
    CommentModule,
    ChecklistModule,
  ],
})
export class AppModule {}
